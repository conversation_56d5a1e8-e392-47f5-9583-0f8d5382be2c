import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { library } from "@fortawesome/fontawesome-svg-core";
import {
    faBook, faStar, faRefresh, faUndo, faTags, faFilter, faRandom,
    faAnglesUp, faAnglesDown, faEdit, faCheckCircle, faCheck, faExclamationTriangle,
    faTimesCircle, faInfoCircle, faCirclePlus, faEye,
    faImage, faMinus, faPlus, faDownload, faTrashCan,
    faCircleXmark, faXmark, faGears, faHome, faSearch, faLayerGroup,
    faArrowUpWideShort, faObjectGroup, faAngleDown,
    faCaretUp, faCaretDown,
} from "@fortawesome/free-solid-svg-icons";

library.add(
    faBook, faStar, faRefresh, faUndo, faTags, faFilter, faRandom,
    faAnglesUp, faAnglesDown, faEdit, faCheckCircle, faCheck, faExclamationTriangle,
    faTimesCircle, faInfoCircle, faCirclePlus, faEye,
    faImage, faMinus, faPlus, faDownload, faTrashCan,
    faCircleXmark, faXmark, faGears, faHome, faSearch, faLayerGroup,
    faArrowUpWideShort, faObjectGroup, faAngleDown,
    faCaretUp, faCaretDown,
);

// 使用方法
// <font-awesome-icon icon="book" />
// <font-awesome-icon icon="fa-solid fa-book" />
// <font-awesome-icon :icon="['fas', 'book']" />

export default FontAwesomeIcon;
