<template>
  <div class="upload-example">
    <h2>文件上传组件示例</h2>
    
    <!-- 示例1：不限制文件类型 -->
    <div class="example-section">
      <h3>示例1：不限制文件类型</h3>
      <my-upload
        ref="upload1"
        @file-selected="handleFileSelected1"
        @file-cleared="handleFileCleared1"
        @error="handleError"
      />
      <div v-if="selectedFile1" class="file-result">
        <p>选择的文件：{{ selectedFile1.name }}</p>
        <button @click="uploadFile1">上传文件</button>
      </div>
    </div>

    <!-- 示例2：只允许图片 -->
    <div class="example-section">
      <h3>示例2：只允许图片文件</h3>
      <my-upload
        :width="300"
        :height="200"
        :accept="['jpg', 'jpeg', 'png', 'gif', 'webp']"
        @file-selected="handleFileSelected2"
        @file-cleared="handleFileCleared2"
        @error="handleError"
      />
      <div v-if="selectedFile2" class="file-result">
        <p>选择的图片：{{ selectedFile2.name }}</p>
        <button @click="uploadFile2">上传图片</button>
      </div>
    </div>

    <!-- 示例3：只允许压缩包 -->
    <div class="example-section">
      <h3>示例3：只允许压缩包（限制大小50MB）</h3>
      <my-upload
        :width="350"
        :height="250"
        :accept="['zip', 'rar', '7z']"
        :max-size="50 * 1024 * 1024"
        @file-selected="handleFileSelected3"
        @file-cleared="handleFileCleared3"
        @error="handleError"
      />
      <div v-if="selectedFile3" class="file-result">
        <p>选择的压缩包：{{ selectedFile3.name }}</p>
        <button @click="uploadFile3">上传压缩包</button>
      </div>
    </div>

    <!-- 示例4：音视频文件 -->
    <div class="example-section">
      <h3>示例4：音视频文件</h3>
      <my-upload
        :accept="['mp3', 'mp4', 'avi', 'mov', 'wav']"
        @file-selected="handleFileSelected4"
        @file-cleared="handleFileCleared4"
        @error="handleError"
      />
      <div v-if="selectedFile4" class="file-result">
        <p>选择的媒体文件：{{ selectedFile4.name }}</p>
        <button @click="uploadFile4">上传媒体文件</button>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <button @click="clearAllFiles">清空所有文件</button>
    </div>

    <!-- 错误信息显示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const upload1 = ref(null)
const selectedFile1 = ref(null)
const selectedFile2 = ref(null)
const selectedFile3 = ref(null)
const selectedFile4 = ref(null)
const errorMessage = ref('')

// 文件选择处理函数
const handleFileSelected1 = (file) => {
  selectedFile1.value = file
  console.log('文件1选择:', file)
  clearError()
}

const handleFileSelected2 = (file) => {
  selectedFile2.value = file
  console.log('图片选择:', file)
  clearError()
}

const handleFileSelected3 = (file) => {
  selectedFile3.value = file
  console.log('压缩包选择:', file)
  clearError()
}

const handleFileSelected4 = (file) => {
  selectedFile4.value = file
  console.log('媒体文件选择:', file)
  clearError()
}

// 文件清空处理函数
const handleFileCleared1 = () => {
  selectedFile1.value = null
  console.log('文件1已清空')
}

const handleFileCleared2 = () => {
  selectedFile2.value = null
  console.log('图片已清空')
}

const handleFileCleared3 = () => {
  selectedFile3.value = null
  console.log('压缩包已清空')
}

const handleFileCleared4 = () => {
  selectedFile4.value = null
  console.log('媒体文件已清空')
}

// 错误处理
const handleError = (error) => {
  errorMessage.value = error.message
  setTimeout(() => {
    errorMessage.value = ''
  }, 3000)
}

const clearError = () => {
  errorMessage.value = ''
}

// 上传文件函数（模拟实际上传）
const uploadFile1 = async () => {
  if (!selectedFile1.value) return
  
  const formData = new FormData()
  formData.append('file', selectedFile1.value)
  
  try {
    // 这里替换为你的实际上传接口
    console.log('开始上传文件1:', selectedFile1.value.name)
    // const response = await fetch('/api/upload', {
    //   method: 'POST',
    //   body: formData
    // })
    // const result = await response.json()
    
    alert('文件上传成功！（模拟）')
  } catch (error) {
    console.error('上传失败:', error)
    alert('上传失败：' + error.message)
  }
}

const uploadFile2 = async () => {
  if (!selectedFile2.value) return
  
  const formData = new FormData()
  formData.append('image', selectedFile2.value)
  
  try {
    console.log('开始上传图片:', selectedFile2.value.name)
    alert('图片上传成功！（模拟）')
  } catch (error) {
    console.error('上传失败:', error)
    alert('上传失败：' + error.message)
  }
}

const uploadFile3 = async () => {
  if (!selectedFile3.value) return
  
  const formData = new FormData()
  formData.append('archive', selectedFile3.value)
  
  try {
    console.log('开始上传压缩包:', selectedFile3.value.name)
    alert('压缩包上传成功！（模拟）')
  } catch (error) {
    console.error('上传失败:', error)
    alert('上传失败：' + error.message)
  }
}

const uploadFile4 = async () => {
  if (!selectedFile4.value) return
  
  const formData = new FormData()
  formData.append('media', selectedFile4.value)
  
  try {
    console.log('开始上传媒体文件:', selectedFile4.value.name)
    alert('媒体文件上传成功！（模拟）')
  } catch (error) {
    console.error('上传失败:', error)
    alert('上传失败：' + error.message)
  }
}

// 清空所有文件
const clearAllFiles = () => {
  upload1.value?.clearFile()
  // 其他上传组件也可以通过 ref 调用 clearFile 方法
}
</script>

<style scoped>
.upload-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  color: #333;
}

.file-result {
  margin-top: 16px;
  padding: 12px;
  background-color: #e8f5e8;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

.file-result p {
  margin: 0 0 8px 0;
  color: #333;
}

.file-result button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.file-result button:hover {
  background-color: #337ecc;
}

.actions {
  text-align: center;
  margin-top: 30px;
}

.actions button {
  background-color: #f56c6c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.actions button:hover {
  background-color: #f45454;
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #f56c6c;
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
</style>
