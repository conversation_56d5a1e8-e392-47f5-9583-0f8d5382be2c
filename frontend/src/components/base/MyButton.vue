<template>
  <button
    class="my-button"
    :class="[
      `is-${type}`,
      `is-${size}`,
      circle ? 'is-circle' : 'is-rounded',
      title ? 'has-tip' : '',
    ]"
    type="button"
    :disabled="disabled"
    :data-tip="title || null"
    v-bind="$attrs"
    @click="onClick"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
// 移除未使用的 computed

interface Props {
  type?: 'normal' | 'primary' | 'success' | 'warning' | 'danger'
  size?: 'mini' | 'small' | 'medium' | 'large'
  disabled?: boolean
  circle?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'normal',
  size: 'medium',
  disabled: false,
  circle: false,
  title: '',
})

const emit = defineEmits<{
  (e: 'click', evt: MouseEvent): void
}>()

function onClick(evt: MouseEvent) {
  if (props.disabled) {
    evt.preventDefault()
    evt.stopPropagation()
    return
  }
  emit('click', evt)
}
</script>

<style scoped lang="scss">
@use 'sass:color';

// 基础色彩（可根据项目变量替换）
$color-normal: white;
$color-primary: #409eff;
$color-success: #67c23a;
$color-warning: #e6a23c;
$color-danger: #f56c6c;
$color-text-on-primary: #ffffff;
$color-border: rgba(0, 0, 0, 0.06);

@mixin btn-variant($bg) {
  background-color: $bg;
  border-color: $bg;
  color: $color-text-on-primary;

  &:hover {
    background-color: color.adjust($bg, $lightness: -3%);
    border-color: color.adjust($bg, $lightness: -3%);
  }
  &:active {
    background-color: color.adjust($bg, $lightness: -8%);
    border-color: color.adjust($bg, $lightness: -8%);
  }
  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba($bg, 0.25);
  }
}

@mixin btn-size($px, $fs) {
  padding: $px;
  font-size: $fs;
}

.my-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-weight: 500;
  line-height: 1;
  border: 1px solid $color-border;
  background-color: #fff;
  color: #333;
  cursor: pointer;
  user-select: none;
  transition:
    background-color 0.15s ease,
    border-color 0.15s ease,
    color 0.15s ease,
    box-shadow 0.15s ease,
    transform 0.02s ease;

  &:hover {
    background-color: #f7f7f7;
  }
  &:active {
    transform: translateY(1px);
  }
  &:disabled,
  &[disabled] {
    cursor: not-allowed;
    opacity: 0.6;
    filter: grayscale(0.1);
    box-shadow: none;
    transform: none;
  }

  // 形状
  &.is-rounded {
    border-radius: 4px;
  }
  &.is-circle {
    border-radius: 50%;
    padding: 0;
    width: 32px;
    height: 32px;
  }

  // 尺寸
  &.is-mini {
    @include btn-size(4px, 8px);
  }
  &.is-small {
    @include btn-size(6px, 12px);
    min-width: 26px;
    min-height: 26px;
  }
  &.is-medium {
    @include btn-size(8px, 14px);
  }
  &.is-large {
    @include btn-size(10px, 16px);
  }

  // 圆形在不同尺寸下的宽高
  &.is-circle.is-mini {
    width: 16px;
    height: 16px;
  }
  &.is-circle.is-small {
    width: 24px;
    height: 24px;
  }
  &.is-circle.is-medium {
    width: 32px;
    height: 32px;
  }
  &.is-circle.is-large {
    width: 40px;
    height: 40px;
  }

  // 颜色变体
  &.is-normal {
    @include btn-variant($color-normal);

    background-color: white;
    border-color: #cdcdcd;
    color: #656565;

    &:hover {
      border-color: #939292;
    }
  }
  &.is-primary {
    @include btn-variant($color-primary);
  }
  &.is-success {
    @include btn-variant($color-success);
  }
  &.is-warning {
    @include btn-variant($color-warning);
  }
  &.is-danger {
    @include btn-variant($color-danger);
  }

  // 轻量悬浮提示（替代原生 title，更快显示）
  &.has-tip {
    position: relative;
  }
  &.has-tip::after {
    content: attr(data-tip);
    position: absolute;
    left: 50%;
    bottom: calc(100% + 8px);
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.85);
    color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.2;
    white-space: nowrap;
    pointer-events: none;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.1s ease,
      visibility 0.1s ease;
    z-index: 9010;
  }
  &.has-tip::before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: calc(100% + 4px);
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.85);
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.1s ease,
      visibility 0.1s ease;
    z-index: 9010;
  }
  &.has-tip:hover::after,
  &.has-tip:hover::before,
  &.has-tip:focus-visible::after,
  &.has-tip:focus-visible::before {
    opacity: 1;
    visibility: visible;
  }
}

// 多个按钮 自动往前添加 间隔
.my-button + .my-button {
  margin-left: $gap;
}
</style>
