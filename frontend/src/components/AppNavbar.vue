<script setup lang="ts">
import { RouterLink } from 'vue-router'
import { ref } from 'vue'

const searchKey = ref('')

// 处理搜索提交
const handleSearch = () => {
  if (!searchKey.value.trim()) return

  location.href = '/?key=' + searchKey.value.trim()
}

const goHome = () => {
  location.href = '/'
}
</script>

<template>
  <div class="navbar-container">
    <nav class="navbar">
      <div class="navbar-header">
        <div class="navbar-brand" @click="goHome">
          <font-awesome-icon class="app-icon" icon="fa-solid fa-book" />
          <div class="app-title">Books</div>
        </div>
        <div class="global-search">
          <my-input v-model="searchKey" placeholder="全局搜索..." clear @keyup.enter="handleSearch">
            <template #prefix>
              <font-awesome-icon icon="fa-solid fa-search"></font-awesome-icon>
            </template>
          </my-input>
        </div>
      </div>

      <div class="navbar-menu">
        <RouterLink to="/" class="nav-link">
          <font-awesome-icon icon="fa-solid fa-home" />
          首页
        </RouterLink>
        <RouterLink to="/subjects" class="nav-link">
          <font-awesome-icon icon="fa-solid fa-layer-group" />
          专题
        </RouterLink>
      </div>
    </nav>
  </div>
</template>

<style scoped lang="scss">
.navbar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9000;
  height: 60px;
  background-color: #ffffff;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .navbar {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;

    .navbar-header {
      display: flex;
      align-items: center;
      height: 100%;
      // justify-content: space-between;
    }
    .navbar-brand {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 16px;
      cursor: pointer;

      &:hover {
        .app-title {
          text-decoration: underline;
        }
      }

      .app-icon {
        color: #1a73e8;
        margin-right: 4px;
      }

      .app-title {
        margin: 0;
        font-size: 15px;
        font-weight: bold;
        color: #333;
      }
    }

    .global-search {
      flex: 1;
      max-width: 400px;
      margin: 0 20px;

      @media (max-width: $media-width) {
        display: none;
      }
    }

    .navbar-menu {
      display: flex;
      gap: 20px;
      min-width: 100px;
      justify-content: flex-end;

      .nav-link {
        color: #666;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: color 0.3s ease;
        padding: 12px 16px;
        border-radius: 4px;

        &:hover {
          color: #42b883;
        }
      }

      .nav-link.router-link-active {
        color: white;
        font-weight: 600;
        background-color: #00a59c;
        border-radius: 8px;
      }
    }
  }
}
</style>
