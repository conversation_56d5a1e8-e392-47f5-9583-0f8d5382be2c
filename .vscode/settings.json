{
  // Python 语言服务器会把 backend 作为额外的模块查找路径，import 就可以直接写 from app.xxx 而不是 from backend.app.xxx
  "python.analysis.extraPaths": ["./backend"],
  // Python 解释器
  "python.defaultInterpreterPath": "./backend/.venv/bin/python",

  // 格式化设置（类似 PyCharm）
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length=88"],
  "editor.formatOnSave": true,

  // Import 排序
  "isort.args": ["--profile", "black"],
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },

  // 代码检查
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "python.linting.flake8Args": ["--max-line-length=88"],

  // 智能提示设置
  "python.analysis.typeCheckingMode": "basic",
  "python.analysis.autoImportCompletions": true,

  // Django 支持
  "python.analysis.autoSearchPaths": true,
  "python.analysis.stubPath": "./backend",
  "python.analysis.diagnosticSeverityOverrides": {
    "reportAttributeAccessIssue": "none"
  },

  // 编辑器行为（类似 PyCharm）
  "editor.rulers": [88],
  "editor.wordWrap": "off",
  "editor.tabSize": 4,
  "editor.insertSpaces": true
}
