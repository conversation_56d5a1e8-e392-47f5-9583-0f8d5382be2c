{
  // Python 语言服务器会把 backend 作为额外的模块查找路径，import 就可以直接写 from app.xxx 而不是 from backend.app.xxx
  "python.analysis.extraPaths": ["./backend"],
  // Python 解释器
  "python.defaultInterpreterPath": "./backend/.venv/bin/python",

  // 格式化设置（使用 autopep8）
  "[python]": {
    "editor.defaultFormatter": "ms-python.autopep8",
    "editor.formatOnSave": true
  },
  "autopep8.args": [
    "--max-line-length=88",
    "--aggressive",
    "--aggressive",
    "--experimental"
  ],

  // Import 排序
  "isort.args": ["--line-length=88", "--profile=django"],
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  },

  // 完全禁用 Pylance
  // "python.languageServer": "None",
  "python.analysis.typeCheckingMode": "off", // 关闭类型检测
  // "python.analysis.autoImportCompletions": false, // 关闭自动导入建议

  // // Django 支持
  // "python.analysis.autoSearchPaths": true,
  // "python.analysis.stubPath": "./backend",
  // "python.analysis.diagnosticSeverityOverrides": {
  //   "reportAttributeAccessIssue": "none"
  // },

  // 编辑器行为（类似 PyCharm）
  "editor.rulers": [88],
  "editor.wordWrap": "off",
  "editor.tabSize": 4,
  "editor.insertSpaces": true
}
